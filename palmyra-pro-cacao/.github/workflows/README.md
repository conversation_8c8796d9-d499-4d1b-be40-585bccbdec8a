# Multi-Stage CI/CD Pipeline Documentation

## Overview

The Palmyra Pro Cacao project uses a multi-stage CI/CD pipeline architecture that breaks down deployment into reusable, maintainable components. This replaces the previous monolithic 280-line workflow with a modular approach.

## Pipeline Architecture

### Main Orchestrator Workflows

#### 1. `create-or-update-environment.yml`
**Purpose**: Main deployment orchestrator
**Triggers**: 
- Pull request events (opened, reopened, edited)
- Manual workflow dispatch

**Stages**:
1. **Setup** → Environment variable computation
2. **Deploy Database** → Database creation and migrations
3. **Deploy Frontend** → Next.js build and Firebase hosting
4. **Deploy Backend** → Go API build and Cloud Run deployment
5. **Notify** → PR comments with deployment status

#### 2. `delete-environment.yml`
**Purpose**: Environment cleanup orchestrator
**Triggers**:
- Pull request closed events
- Manual workflow dispatch

**Stages**:
1. **Setup** → Environment variable computation
2. **Delete Frontend** → Firebase hosting channel cleanup
3. **Delete Backend** → Cloud Run service removal
4. **Delete Database** → Cloud SQL database deletion
5. **Notify** → PR comments with cleanup status

### Reusable Workflow Components

#### 1. `setup-environment.yml`
**Purpose**: Centralized environment variable computation
**Inputs**: environment, shortName, pr_number, pr_user
**Outputs**: All computed environment variables (short_name, tag_name, channel_id, etc.)

**Key Logic**:
- Production/Staging/PR environment detection
- Resource naming conventions
- URL generation
- Security validations

#### 2. `deploy-database.yml`
**Purpose**: Database deployment and migrations
**Inputs**: environment, short_name, db_name, seed_dev
**Outputs**: database_created

**Key Features**:
- Cloud SQL Proxy setup
- Database existence checking
- DDL migrations
- Optional data seeding
- Idempotent operations

#### 3. `deploy-frontend.yml`
**Purpose**: Frontend build and Firebase deployment
**Inputs**: environment, short_name, channel_id, api_url
**Outputs**: preview_url

**Key Features**:
- Next.js build with API specs generation
- Firebase hosting deployment
- Live vs preview channel handling
- Build size reporting

#### 4. `deploy-backend.yml`
**Purpose**: Backend API build and Cloud Run deployment
**Inputs**: environment, short_name, tag_name, db_name, min_instances, fe_url
**Outputs**: service_url

**Key Features**:
- Docker image build and push
- Cloud Run deployment with secrets
- IAM policy configuration
- CORS configuration with frontend URL

## Benefits of Multi-Stage Architecture

### 1. **Maintainability**
- ✅ Smaller, focused workflow files
- ✅ Single responsibility principle
- ✅ Easier debugging and troubleshooting
- ✅ Clear separation of concerns

### 2. **Reusability**
- ✅ Workflows can be called independently
- ✅ Shared logic in reusable components
- ✅ Consistent deployment patterns
- ✅ Easy to add new environments

### 3. **Reliability**
- ✅ Better error isolation
- ✅ Parallel execution where possible
- ✅ Dependency management between stages
- ✅ Granular failure reporting

### 4. **Observability**
- ✅ Clear stage-by-stage progress
- ✅ Detailed deployment summaries
- ✅ Structured PR comments
- ✅ Better logging and debugging

## Environment Types

### Production (`prod`)
- **URL**: `https://nn-cacao.palmyra.pro` ⚠️ *Needs NN reference fix*
- **Firebase Channel**: `live`
- **Docker Tag**: `latest`
- **Min Instances**: Default
- **Requires**: `production` environment approval

### Staging (`staging`)
- **URL**: `https://nn-cacao-dev.web.app` ⚠️ *Needs NN reference fix*
- **Firebase Channel**: `live`
- **Docker Tag**: `staging-latest`
- **Min Instances**: 1 (always running)

### Pull Request (`PR`)
- **URL**: Dynamic Firebase preview channel
- **Firebase Channel**: `pr-{number}-{username}` (sanitized)
- **Docker Tag**: Same as channel name
- **Min Instances**: 0 (scales to zero)

## Security Features

### 1. **Environment Protection**
- Production deployments require `production` environment
- Staging has dedicated configuration
- PR environments are isolated

### 2. **Secret Management**
- GitHub Secrets for CI/CD authentication
- GCP Secret Manager for runtime secrets
- Volume mounting for Firebase service accounts

### 3. **Access Control**
- Environment-specific approvals
- IAM policy management
- Secure credential handling

## Usage Examples

### Manual Deployment
```bash
gh workflow run .github/workflows/create-or-update-environment.yml \
  -f environment="development" \
  -f shortName="test-feature" \
  -f seedDev=true
```

### Manual Cleanup
```bash
gh workflow run .github/workflows/delete-environment.yml \
  -f environment="development" \
  -f shortName="test-feature"
```

## Migration Notes

### What Changed
- ✅ Monolithic workflow split into 5 focused files
- ✅ Improved error handling and reporting
- ✅ Better dependency management
- ✅ Enhanced PR notifications

### What Stayed the Same
- ✅ All deployment logic and configurations
- ✅ Environment variable computation
- ✅ Security and secret handling
- ✅ Database migration strategy

### Known Issues to Fix
- ⚠️ NN references in production/staging URLs
- ⚠️ Firebase project naming inconsistency
- ⚠️ Hardcoded Cloud SQL Proxy version

## Future Improvements

### Short Term
- [ ] Fix legacy NN URL references
- [ ] Add workflow validation tests
- [ ] Implement rollback workflows

### Medium Term
- [ ] Add blue-green deployment support
- [ ] Implement automated testing stage
- [ ] Add performance monitoring

### Long Term
- [ ] Infrastructure as Code (Terraform)
- [ ] Multi-region deployment
- [ ] Advanced observability and alerting
