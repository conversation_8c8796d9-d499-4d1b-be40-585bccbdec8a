name: Create or Update an environment
run-name: >
  Create or Update ${{ inputs.shortName != '' && inputs.shortName || format('pr-{0}-{1}', github.event.pull_request.number, github.event.pull_request.user.login) }}

on:
  pull_request:
    types: [opened, reopened, edited]
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: "Environment"
        required: true
        default: "development"
      shortName:
        type: string
        description: "staging, prod or any other short name used as prefix for resources, labels, etc."
        required: true
        default: "PR"
      seedDev:
        type: boolean
        description: "Seed database with dev data"
        required: true
        default: true

jobs:
  setup:
    name: Setup Environment Variables
    uses: ./.github/workflows/setup-environment.yml
    with:
      environment: ${{ inputs.environment || 'development' }}
      shortName: ${{ inputs.shortName || 'PR' }}
      pr_number: ${{ github.event.pull_request.number }}
      pr_user: ${{ github.event.pull_request.user.login }}
    secrets: inherit

  deploy-database:
    name: Deploy Database
    needs: setup
    uses: ./.github/workflows/deploy-database.yml
    with:
      environment: ${{ inputs.environment || 'development' }}
      short_name: ${{ needs.setup.outputs.short_name }}
      db_name: ${{ needs.setup.outputs.db_name }}
      seed_dev: ${{ inputs.seedDev || true }}
    secrets: inherit

  deploy-frontend:
    name: Deploy Frontend
    needs: [setup, deploy-database]
    uses: ./.github/workflows/deploy-frontend.yml
    with:
      environment: ${{ inputs.environment || 'development' }}
      short_name: ${{ needs.setup.outputs.short_name }}
      channel_id: ${{ needs.setup.outputs.channel_id }}
      api_url: ${{ needs.setup.outputs.api_url }}
    secrets: inherit

  deploy-backend:
    name: Deploy Backend API
    needs: [setup, deploy-database, deploy-frontend]
    uses: ./.github/workflows/deploy-backend.yml
    with:
      environment: ${{ inputs.environment || 'development' }}
      short_name: ${{ needs.setup.outputs.short_name }}
      tag_name: ${{ needs.setup.outputs.tag_name }}
      db_name: ${{ needs.setup.outputs.db_name }}
      min_instances: ${{ needs.setup.outputs.min_instances }}
      fe_url: ${{ needs.deploy-frontend.outputs.preview_url }}
    secrets: inherit

  notify:
    name: Notify Deployment Status
    needs: [setup, deploy-frontend, deploy-backend]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Comment on PR with deployment URLs
        if: github.event_name == 'pull_request'
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ## 🚀 Deployment Status

            **Environment**: `${{ needs.setup.outputs.short_name }}`

            | Service | Status | URL |
            |---------|--------|-----|
            | Frontend | ${{ needs.deploy-frontend.result == 'success' && '✅' || '❌' }} | ${{ needs.deploy-frontend.outputs.preview_url }} |
            | API | ${{ needs.deploy-backend.result == 'success' && '✅' || '❌' }} | ${{ needs.setup.outputs.api_url }} |
            | Database | ${{ needs.deploy-database.result == 'success' && '✅' || '❌' }} | `${{ needs.setup.outputs.db_name }}` |

