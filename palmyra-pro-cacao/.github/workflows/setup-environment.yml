name: Setup Environment Variables

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "Environment name"
      shortName:
        required: true
        type: string
        description: "Short name for resources"
      pr_number:
        required: false
        type: string
        description: "PR number"
      pr_user:
        required: false
        type: string
        description: "PR user"
    outputs:
      short_name:
        description: "Computed short name"
        value: ${{ jobs.setup.outputs.short_name }}
      tag_name:
        description: "Docker tag name"
        value: ${{ jobs.setup.outputs.tag_name }}
      channel_id:
        description: "Firebase channel ID"
        value: ${{ jobs.setup.outputs.channel_id }}
      db_name:
        description: "Database name"
        value: ${{ jobs.setup.outputs.db_name }}
      api_url:
        description: "API URL"
        value: ${{ jobs.setup.outputs.api_url }}
      fe_url:
        description: "Frontend URL"
        value: ${{ jobs.setup.outputs.fe_url }}
      min_instances:
        description: "Minimum instances"
        value: ${{ jobs.setup.outputs.min_instances }}
      db_instance_connection_name:
        description: "Database connection name"
        value: ${{ jobs.setup.outputs.db_instance_connection_name }}

jobs:
  setup:
    name: Setup Environment Variables
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    outputs:
      short_name: ${{ steps.compute-vars.outputs.short_name }}
      tag_name: ${{ steps.compute-vars.outputs.tag_name }}
      channel_id: ${{ steps.compute-vars.outputs.channel_id }}
      db_name: ${{ steps.compute-vars.outputs.db_name }}
      api_url: ${{ steps.compute-vars.outputs.api_url }}
      fe_url: ${{ steps.compute-vars.outputs.fe_url }}
      min_instances: ${{ steps.compute-vars.outputs.min_instances }}
      db_instance_connection_name: ${{ steps.compute-vars.outputs.db_instance_connection_name }}
    steps:
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Compute Environment Variables
        id: compute-vars
        run: |
          # If inputs.shortName is not present, it means its a PR.
          INPUTS_SHORT_NAME="${{ inputs.shortName }}"

          if [[ "${INPUTS_SHORT_NAME}" == "prod" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="latest"
            FE_URL="https://nn-cacao.palmyra.pro"
            
            # To avoid mistakes, let's don't allow prod deployment into development.
            if [[ "${{ inputs.environment }}" != "production" ]]; then
              echo "❌ Production deployment only allowed in production environment"
              exit 1
            fi
          elif [[ "${INPUTS_SHORT_NAME}" == "staging" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="staging-latest"
            FE_URL="https://nn-cacao-dev.web.app"
          elif [[ "${INPUTS_SHORT_NAME}" == "PR" ]]; then
            RAW_NAME="pr-${{ inputs.pr_number }}-${{ inputs.pr_user }}"
            SHORT_NAME=$(echo "${RAW_NAME}" | tr '[:upper:]' '[:lower:]' | tr -c 'a-z0-9-' '-' | cut -c1-40)
            CHANNEL_ID="${SHORT_NAME}"
            TAG_NAME="${SHORT_NAME}"
            FE_URL=""
          else
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="${INPUTS_SHORT_NAME}"
            TAG_NAME="${INPUTS_SHORT_NAME}"
            FE_URL=""
          fi

          # Set minimum instances
          if [[ "$SHORT_NAME" == "staging" ]]; then
            MIN_INSTANCES=1
          else
            MIN_INSTANCES=0
          fi

          # Get GCP project number
          GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')

          # Compute derived values
          DB_NAME="palmyrapro-db-$SHORT_NAME"
          DB_INSTANCE_CONNECTION_NAME="${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
          API_URL="https://$SHORT_NAME-cacao-api-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app"

          # Output all variables
          echo "short_name=$SHORT_NAME" >> $GITHUB_OUTPUT
          echo "tag_name=$TAG_NAME" >> $GITHUB_OUTPUT
          echo "channel_id=$CHANNEL_ID" >> $GITHUB_OUTPUT
          echo "db_name=$DB_NAME" >> $GITHUB_OUTPUT
          echo "api_url=$API_URL" >> $GITHUB_OUTPUT
          echo "fe_url=$FE_URL" >> $GITHUB_OUTPUT
          echo "min_instances=$MIN_INSTANCES" >> $GITHUB_OUTPUT
          echo "db_instance_connection_name=$DB_INSTANCE_CONNECTION_NAME" >> $GITHUB_OUTPUT

          # Log computed values for debugging
          echo "✅ Environment Variables Computed:"
          echo "  SHORT_NAME: $SHORT_NAME"
          echo "  TAG_NAME: $TAG_NAME"
          echo "  CHANNEL_ID: $CHANNEL_ID"
          echo "  DB_NAME: $DB_NAME"
          echo "  API_URL: $API_URL"
          echo "  FE_URL: $FE_URL"
          echo "  MIN_INSTANCES: $MIN_INSTANCES"
