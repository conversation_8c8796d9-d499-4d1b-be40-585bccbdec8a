name: Delete an environment
run-name: >
  Delete ${{ inputs.shortName != '' && inputs.shortName || format('pr-{0}-{1}', github.event.pull_request.number, github.event.pull_request.user.login) }}

on:
  pull_request:
    types: [closed]
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: "Environment"
        required: true
        default: "development"
      shortName:
        type: string
        description: "Short name used, for example, as prefix for resources, labels, etc."
        required: true
        default: "PR"

jobs:
  setup:
    name: Setup Environment Variables
    uses: ./.github/workflows/setup-environment.yml
    with:
      environment: ${{ inputs.environment || 'development' }}
      shortName: ${{ inputs.shortName || 'PR' }}
      pr_number: ${{ github.event.pull_request.number }}
      pr_user: ${{ github.event.pull_request.user.login }}
    secrets: inherit

  delete-frontend:
    name: Delete Frontend
    needs: setup
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Delete Firebase Hosting Channel
        working-directory: apps/frontend
        run: |
          npm install -g firebase-tools

          if [ "${{ needs.setup.outputs.channel_id }}" != "live" ]; then
            echo "Deleting Firebase channel: ${{ needs.setup.outputs.channel_id }}"
            firebase hosting:channel:delete "${{ needs.setup.outputs.channel_id }}" --project="${{ vars.GCP_PROJECT_ID }}" || echo "Channel may not exist"
            echo "✅ Firebase channel deleted"
          else
            echo "⚠️ Skipping live channel deletion"
          fi

  delete-backend:
    name: Delete Backend
    needs: setup
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    steps:
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Delete Cloud Run Service
        run: |
          SERVICE_NAME="${{ needs.setup.outputs.short_name }}-cacao-api"
          echo "Deleting Cloud Run service: $SERVICE_NAME"

          gcloud run services delete $SERVICE_NAME --quiet \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} || echo "Service may not exist"

          echo "✅ Cloud Run service deleted"

  delete-database:
    name: Delete Database
    needs: [setup, delete-frontend, delete-backend]
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    env:
      DB_INSTANCE_CONNECTION_NAME: "${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
    steps:
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Install Cloud SQL Auth Proxy
        run: |
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.15.2/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy
          sudo mv cloud-sql-proxy /usr/local/bin/

      - name: Start Cloud SQL Proxy
        run: |
          cloud-sql-proxy $DB_INSTANCE_CONNECTION_NAME --credentials-file=$GOOGLE_APPLICATION_CREDENTIALS --port 5432 &

          # Wait and check if port is open
          for i in {1..10}; do
            echo "Checking if proxy is up (attempt $i)..."
            nc -z 127.0.0.1 5432 && break
            sleep 2
          done

          # Final check
          if ! nc -z 127.0.0.1 5432; then
            echo "❌ Cloud SQL Proxy failed to start."
            exit 1
          fi

          echo "✅ Cloud SQL Proxy is up and running."

      - name: Check if database exists
        id: check-db
        env:
          PGPASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          DB_EXISTS=$(psql -h 127.0.0.1 -U ${{ vars.DB_USER }} -d postgres \
            -tAc "SELECT 1 FROM pg_database WHERE datname = '${{ needs.setup.outputs.db_name }}'" | grep -q 1 && echo true || echo false)

          echo "Database exists: $DB_EXISTS"
          echo "exists=$DB_EXISTS" >> $GITHUB_OUTPUT

      - name: Delete Cloud SQL Database
        if: steps.check-db.outputs.exists == 'true'
        run: |
          echo "Deleting database: ${{ needs.setup.outputs.db_name }}"
          gcloud sql databases delete ${{ needs.setup.outputs.db_name }} --instance=${{ vars.DB_INSTANCE }} --quiet
          echo "✅ Database deleted successfully"

  notify:
    name: Notify Deletion Status
    needs: [setup, delete-frontend, delete-backend, delete-database]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Comment on PR with deletion status
        if: github.event_name == 'pull_request'
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ## 🗑️ Environment Cleanup Complete

            **Environment**: `${{ needs.setup.outputs.short_name }}`

            | Service | Status |
            |---------|--------|
            | Frontend | ${{ needs.delete-frontend.result == 'success' && '✅ Deleted' || '❌ Failed' }} |
            | API | ${{ needs.delete-backend.result == 'success' && '✅ Deleted' || '❌ Failed' }} |
            | Database | ${{ needs.delete-database.result == 'success' && '✅ Deleted' || '❌ Failed' }} |
