name: Deploy Backend API

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "Environment name"
      short_name:
        required: true
        type: string
        description: "Short name for resources"
      tag_name:
        required: true
        type: string
        description: "Docker tag name"
      db_name:
        required: true
        type: string
        description: "Database name"
      min_instances:
        required: true
        type: string
        description: "Minimum instances"
      fe_url:
        required: false
        type: string
        description: "Frontend URL for CORS"
    outputs:
      service_url:
        description: "Cloud Run service URL"
        value: ${{ jobs.deploy-backend.outputs.service_url }}

jobs:
  deploy-backend:
    name: Deploy Backend API
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    outputs:
      service_url: ${{ steps.output.outputs.service_url }}
    env:
      DB_INSTANCE_CONNECTION_NAME: "${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Configure Docker
        run: gcloud auth configure-docker "${{ vars.GCP_REGION }}-docker.pkg.dev"

      - name: Build and Push Docker Image
        run: |
          IMAGE_NAME="${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ inputs.tag_name }}"
          
          echo "Building Docker image: $IMAGE_NAME"
          docker build -t $IMAGE_NAME -f ./apps/api/Dockerfile .
          
          echo "Pushing Docker image..."
          docker push $IMAGE_NAME
          
          echo "✅ Docker image built and pushed successfully"

      - name: Deploy to Cloud Run
        id: deploy
        run: |
          SERVICE_NAME="${{ inputs.short_name }}-cacao-api"
          IMAGE_NAME="${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ inputs.tag_name }}"
          
          # Compute GCP project number for service URL
          GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')
          SERVICE_URL="https://$SERVICE_NAME-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app"
          
          # Compute database password secret name
          DB_PASSWORD_SECRET_NAME="DB_PASSWORD_$(echo "${{ vars.DB_INSTANCE }}" | tr '[:lower:]' '[:upper:]' | tr '-' '_')"
          
          echo "Deploying Cloud Run service: $SERVICE_NAME"
          echo "Image: $IMAGE_NAME"
          echo "Service URL: $SERVICE_URL"
          
          gcloud run deploy $SERVICE_NAME \
            --image $IMAGE_NAME \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --execution-environment gen2 \
            --min-instances ${{ inputs.min_instances }} \
            --max-instances 2 \
            --concurrency 1000 \
            --allow-unauthenticated \
            --port 3000 \
            --add-cloudsql-instances $DB_INSTANCE_CONNECTION_NAME \
            --set-env-vars "DB_NAME=${{ inputs.db_name }},DB_HOST=/cloudsql/${DB_INSTANCE_CONNECTION_NAME},DB_PORT=${{ vars.DB_PORT }},DB_USER=${{ vars.DB_USER }},DB_SSL_MODE=${{ vars.DB_SSL_MODE }}" \
            --set-env-vars "ALLOWED_ORIGIN=${{ inputs.fe_url }},METABASE_SITE_URL=${{ vars.METABASE_SITE_URL }},ENV_SHORT_NAME=${{ inputs.short_name }}" \
            --set-env-vars "FIREBASE_ADMIN_KEY_PATH=/secrets/FIREBASE_SERVICE_ACCOUNT_KEY,FIREBASE_AUTH_SECRET=${{ vars.FIREBASE_AUTH_SECRET }},FE_URL=${{ inputs.fe_url }}" \
            --set-secrets "/secrets/FIREBASE_SERVICE_ACCOUNT_KEY=FIREBASE_SERVICE_ACCOUNT_KEY:latest,DB_PASSWORD=${DB_PASSWORD_SECRET_NAME}:latest,METABASE_SECRET_KEY=METABASE_SECRET_KEY:latest" \
            --labels "env_short_name=${{ inputs.short_name }}"

          echo "✅ Cloud Run service deployed successfully"

      - name: Configure IAM Policy
        run: |
          SERVICE_NAME="${{ inputs.short_name }}-cacao-api"
          
          echo "Setting IAM policy for public access..."
          gcloud run services add-iam-policy-binding $SERVICE_NAME \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --member="allUsers" \
            --role="roles/run.invoker"
          
          echo "✅ IAM policy configured"

      - name: Output Service URL
        id: output
        run: |
          GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')
          SERVICE_URL="https://${{ inputs.short_name }}-cacao-api-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app"
          echo "service_url=$SERVICE_URL" >> $GITHUB_OUTPUT

      - name: Backend Deployment Summary
        run: |
          GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')
          SERVICE_URL="https://${{ inputs.short_name }}-cacao-api-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app"
          
          echo "## 🚀 Backend Deployment Summary"
          echo "- **Service**: ${{ inputs.short_name }}-cacao-api"
          echo "- **URL**: $SERVICE_URL"
          echo "- **Image**: ${{ inputs.tag_name }}"
          echo "- **Database**: ${{ inputs.db_name }}"
          echo "- **Min Instances**: ${{ inputs.min_instances }}"
          echo "- **Environment**: ${{ inputs.environment }}"
