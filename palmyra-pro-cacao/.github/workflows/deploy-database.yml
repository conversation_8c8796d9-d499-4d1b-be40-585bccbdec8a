name: Deploy Database

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "Environment name"
      short_name:
        required: true
        type: string
        description: "Short name for resources"
      db_name:
        required: true
        type: string
        description: "Database name"
      seed_dev:
        required: false
        type: boolean
        default: true
        description: "Seed database with dev data"
    outputs:
      database_created:
        description: "Whether database was created"
        value: ${{ jobs.deploy-database.outputs.database_created }}

jobs:
  deploy-database:
    name: Deploy Database
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    outputs:
      database_created: ${{ steps.check-db.outputs.created }}
    env:
      DB_INSTANCE_CONNECTION_NAME: "${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Install Cloud SQL Auth Proxy
        run: |
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.15.2/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy
          sudo mv cloud-sql-proxy /usr/local/bin/

      - name: Start Cloud SQL Proxy
        run: |
          cloud-sql-proxy $DB_INSTANCE_CONNECTION_NAME --credentials-file=$GOOGLE_APPLICATION_CREDENTIALS --port 5432 &

          # Wait and check if port is open
          for i in {1..10}; do
            echo "Checking if proxy is up (attempt $i)..."
            nc -z 127.0.0.1 5432 && break
            sleep 2
          done

          # Final check
          if ! nc -z 127.0.0.1 5432; then
            echo "❌ Cloud SQL Proxy failed to start."
            exit 1
          fi

          echo "✅ Cloud SQL Proxy is up and running."

      - name: Check if database exists
        id: check-db
        env:
          PGPASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          DB_EXISTS=$(psql -h 127.0.0.1 -U ${{ vars.DB_USER }} -d postgres \
            -tAc "SELECT 1 FROM pg_database WHERE datname = '${{ inputs.db_name }}'" | grep -q 1 && echo true || echo false)

          echo "Database exists: $DB_EXISTS"
          echo "exists=$DB_EXISTS" >> $GITHUB_OUTPUT
          
          if [[ "$DB_EXISTS" == "false" ]]; then
            echo "created=true" >> $GITHUB_OUTPUT
          else
            echo "created=false" >> $GITHUB_OUTPUT
          fi

      - name: Create Cloud SQL Database
        if: steps.check-db.outputs.exists == 'false'
        run: |
          echo "Creating database: ${{ inputs.db_name }}"
          gcloud sql databases create ${{ inputs.db_name }} --instance=${{ vars.DB_INSTANCE }}
          echo "✅ Database created successfully"

      - name: Run Database Migrations
        if: steps.check-db.outputs.exists == 'false'
        env:
          PGPASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          echo "Running DDL migrations..."
          psql "host=127.0.0.1 port=5432 user=${{ vars.DB_USER }} dbname=${{ inputs.db_name }}" -f "packages/db/schema/01-ddl.sql"
          echo "✅ DDL migrations completed"

      - name: Seed Database
        if: steps.check-db.outputs.exists == 'false' && inputs.seed_dev
        env:
          PGPASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          echo "Seeding database with initial data..."
          psql "host=127.0.0.1 port=5432 user=${{ vars.DB_USER }} dbname=${{ inputs.db_name }}" -f "packages/db/schema/02-seeding.sql"
          echo "✅ Database seeding completed"

      - name: Database Deployment Summary
        run: |
          echo "## 🗄️ Database Deployment Summary"
          echo "- **Database Name**: ${{ inputs.db_name }}"
          echo "- **Instance**: ${{ vars.DB_INSTANCE }}"
          echo "- **Created**: ${{ steps.check-db.outputs.created }}"
          echo "- **Seeded**: ${{ inputs.seed_dev && steps.check-db.outputs.created }}"
          echo "- **Environment**: ${{ inputs.environment }}"
