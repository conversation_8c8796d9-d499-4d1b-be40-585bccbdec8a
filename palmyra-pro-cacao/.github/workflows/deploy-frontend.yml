name: Deploy Frontend

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "Environment name"
      short_name:
        required: true
        type: string
        description: "Short name for resources"
      channel_id:
        required: true
        type: string
        description: "Firebase channel ID"
      api_url:
        required: true
        type: string
        description: "Backend API URL"
    outputs:
      preview_url:
        description: "Frontend preview URL"
        value: ${{ jobs.deploy-frontend.outputs.preview_url }}

jobs:
  deploy-frontend:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    outputs:
      preview_url: ${{ steps.deploy.outputs.preview_url }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Build Frontend
        env:
          NEXT_PUBLIC_BACKEND_URL: "${{ inputs.api_url }}"
          CI: "true"
          SKIP_TYPE_CHECK: "true"
          # Firebase configuration
          NEXT_PUBLIC_FIREBASE_API_KEY: "${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}"
          NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: "${{ vars.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}"
          NEXT_PUBLIC_FIREBASE_PROJECT_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}"
          NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: "${{ vars.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}"
          NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}"
          NEXT_PUBLIC_FIREBASE_APP_ID: "${{ secrets.NEXT_PUBLIC_FIREBASE_APP_ID }}"
        run: |
          echo "Installing dependencies..."
          npm ci
          
          echo "Generating API specs..."
          npm run generate --workspace=packages/api-specs
          
          echo "Building frontend..."
          npm run build --workspace=apps/frontend
          
          echo "Build size:"
          du -sh apps/frontend/dist/.
          
          echo "✅ Frontend build completed"

      - name: Deploy to Firebase Hosting
        id: deploy
        working-directory: apps/frontend
        env:
          NODE_OPTIONS: "--trace-deprecation"
        run: |
          echo "Installing Firebase CLI..."
          npm install -g firebase-tools

          # Deploy
          TMP_OUTPUT=$(mktemp)
          EXIT_CODE=0

          if [ "${{ inputs.channel_id }}" == "live" ]; then
            echo "Deploying to live channel..."
            firebase deploy \
              --only hosting \
              --project="${{ vars.GCP_PROJECT_ID }}" \
              --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract preview URL for live deployment
            PREVIEW_URL="https://${{ vars.GCP_PROJECT_ID }}.web.app/"
            echo "✅ Live deployment URL: $PREVIEW_URL"
            echo "preview_url=$PREVIEW_URL" >> "$GITHUB_OUTPUT"

          else
            echo "Deploying to preview channel: ${{ inputs.channel_id }}"
            firebase hosting:channel:deploy "${{ inputs.channel_id }}" \
              --project="${{ vars.GCP_PROJECT_ID }}" \
              --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract preview URL from output
            PREVIEW_URL=$(jq -r '.result[].url' ${TMP_OUTPUT})
            echo "✅ Preview URL: $PREVIEW_URL"
            echo "preview_url=$PREVIEW_URL" >> "$GITHUB_OUTPUT"
          fi

          rm "$TMP_OUTPUT"

      - name: Frontend Deployment Summary
        run: |
          echo "## 🌐 Frontend Deployment Summary"
          echo "- **Environment**: ${{ inputs.environment }}"
          echo "- **Channel**: ${{ inputs.channel_id }}"
          echo "- **URL**: ${{ steps.deploy.outputs.preview_url }}"
          echo "- **Backend API**: ${{ inputs.api_url }}"
          echo "- **Firebase Project**: ${{ vars.GCP_PROJECT_ID }}"
